# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import json
import os

from flask import Flask, jsonify, render_template, request, send_from_directory
from services.ai_service import AIService
from services.pbiembedservice import PbiEmbedService
from utils import Utils

# Initialize the Flask app
app = Flask(__name__)

# Load configuration
app.config.from_object("config.BaseConfig")


@app.route("/")
def index():
    """Returns a static HTML page"""

    return render_template("index.html")


@app.route("/getembedinfo", methods=["GET"])
def get_embed_info():
    """Returns report embed configuration"""

    config_result = Utils.check_config(app)
    if config_result is not None:
        return json.dumps({"errorMsg": config_result}), 500
    try:
        embed_info = PbiEmbedService().get_embed_params_for_single_report(
            app.config["WORKSPACE_ID"], app.config["REPORT_ID"]
        )
        return embed_info
    except Exception as ex:
        return json.dumps({"errorMsg": str(ex)}), 500


@app.route("/generate-summary", methods=["POST"])
def generate_summary():
    """Generate AI-powered summary from Power BI data"""

    try:
        # Get the data from the request
        data = request.get_json()
        if not data:
            return jsonify({"errorMsg": "No data provided"}), 400

        # Initialize AI service
        ai_service = AIService()

        # Clean and format the data
        cleaned_data = ai_service.clean_and_format_data(data)

        if not cleaned_data:
            return jsonify({"errorMsg": "No valid data found for analysis"}), 400

        # Generate comprehensive summary
        summary_result = ai_service.generate_comprehensive_summary(cleaned_data)

        return jsonify(
            {
                "success": True,
                "summary": summary_result,
                "message": "AI summary generated successfully!",
            }
        )

    except ValueError as ve:
        return jsonify({"errorMsg": f"Configuration error: {str(ve)}"}), 500
    except Exception as ex:
        return jsonify({"errorMsg": f"Error generating AI summary: {str(ex)}"}), 500


@app.route("/summary")
def summary():
    """Returns the summary display page"""
    return render_template("summary.html")


@app.route("/favicon.ico", methods=["GET"])
def getfavicon():
    """Returns path of the favicon to be rendered"""

    return send_from_directory(
        os.path.join(app.root_path, "static"),
        "img/favicon.ico",
        mimetype="image/vnd.microsoft.icon",
    )


if __name__ == "__main__":
    app.run(debug=True)
