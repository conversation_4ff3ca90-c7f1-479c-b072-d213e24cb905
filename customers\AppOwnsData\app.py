# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import json
import os

import requests
from flask import Flask, jsonify, render_template, request, send_from_directory
from services.pbiembedservice import PbiEmbedService
from utils import Utils

# Initialize the Flask app
app = Flask(__name__)

# Load configuration
app.config.from_object("config.BaseConfig")


@app.route("/")
def index():
    """Returns a static HTML page"""

    return render_template("index.html")


@app.route("/getembedinfo", methods=["GET"])
def get_embed_info():
    """Returns report embed configuration"""

    config_result = Utils.check_config(app)
    if config_result is not None:
        return json.dumps({"errorMsg": config_result}), 500
    try:
        embed_info = PbiEmbedService().get_embed_params_for_single_report(
            app.config["WORKSPACE_ID"], app.config["REPORT_ID"]
        )
        return embed_info
    except Exception as ex:
        return json.dumps({"errorMsg": str(ex)}), 500


@app.route("/sendtoflow", methods=["POST"])
def send_to_flow():
    """Sends data to Power Automate flow with proper authentication"""

    try:
        # Get the data from the request
        data = request.get_json()
        if not data:
            return jsonify({"errorMsg": "No data provided"}), 400

        # Prepare headers for Power Automate request
        headers = {"Content-Type": "application/json"}

        # Get the Power Automate flow URL
        flow_url = app.config.get("POWER_AUTOMATE_FLOW_URL")
        if not flow_url:
            return jsonify({"errorMsg": "Power Automate flow URL not configured"}), 500

        # Get the configured HTTP method
        http_method = app.config.get("POWER_AUTOMATE_HTTP_METHOD", "POST").upper()

        # Make the request to Power Automate
        if http_method == "GET":
            response = requests.get(flow_url, json=data, headers=headers, timeout=30)
        else:
            # For GET requests, we need to pass data as query parameters
            query_params = {}
            if isinstance(data, dict) and "data" in data:
                # Flatten the data structure for query parameters
                chart_data = data["data"]
                if isinstance(chart_data, str):
                    query_params["data"] = chart_data
                else:
                    # Convert complex data to JSON string
                    query_params["data"] = json.dumps(chart_data)
            else:
                query_params["data"] = json.dumps(data)

            # Remove Content-Type header for GET requests
            get_headers = {k: v for k, v in headers.items() if k != "Content-Type"}

            # Step 1: Parse the outer JSON string
            inner_data = json.loads(query_params["data"])

            # Step 2: Clean each "data" field by replacing escaped characters
            for key, value in inner_data.items():
                if "data" in value:
                    # Replace \r\n with actual newlines for better readability or CSV processing
                    value["data"] = value["data"].replace("\\r\\n", "\n")
                    value["data"] = value["data"].replace("\r", "\n")

            # Now `inner_data` is a clean dictionary
            # You can assign it back or send it as JSON
            cleaned_query_params = {"data": inner_data}

            # Example: convert to JSON string if you're sending it via an API
            json_to_send = json.dumps(cleaned_query_params)

            print("Params: ", json_to_send)
            response = requests.post(
                flow_url,
                params=json_to_send,
                headers=get_headers,
                timeout=30,
            )

        # Check if the request was successful
        if response.status_code == 200 or response.status_code == 202:
            return jsonify({"message": "Data sent to Power Automate successfully!"})
        else:
            return (
                jsonify(
                    {
                        "errorMsg": f"Power Automate request failed with status {response.status_code}: {response.text}"
                    }
                ),
                response.status_code,
            )

    except requests.exceptions.RequestException as ex:
        return jsonify({"errorMsg": f"Network error: {str(ex)}"}), 500
    except Exception as ex:
        return (
            jsonify({"errorMsg": f"Error sending data to Power Automate: {str(ex)}"}),
            500,
        )


@app.route("/favicon.ico", methods=["GET"])
def getfavicon():
    """Returns path of the favicon to be rendered"""

    return send_from_directory(
        os.path.join(app.root_path, "static"),
        "img/favicon.ico",
        mimetype="image/vnd.microsoft.icon",
    )


if __name__ == "__main__":
    app.run()
