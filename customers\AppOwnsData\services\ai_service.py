# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import json
import os
import re
from typing import Any, Dict, List

import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class AIService:
    """Service for handling AI-powered data analysis and summary generation"""

    def __init__(self):
        """Initialize the AI service with Google Generative AI"""
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in environment variables")

        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel("gemini-2.0-flash")

    def clean_and_format_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and format the raw Power BI data for AI processing

        Args:
            raw_data: Raw data from Power BI visuals

        Returns:
            Cleaned and formatted data
        """
        cleaned_data = {}

        if "data" in raw_data:
            visuals_data = raw_data["data"]

            for visual_key, visual_info in visuals_data.items():
                if isinstance(visual_info, dict) and "data" in visual_info:
                    # Clean the CSV-like data
                    raw_csv = visual_info["data"]
                    cleaned_csv = self._clean_csv_data(raw_csv)

                    cleaned_data[visual_key] = {
                        "title": visual_info.get("title", visual_key),
                        "data": cleaned_csv,
                        "exportedAt": visual_info.get("exportedAt", ""),
                        "structured_data": self._parse_csv_to_structured(cleaned_csv),
                    }

        return cleaned_data

    def _clean_csv_data(self, csv_data: str) -> str:
        """Clean CSV data by removing escape characters and formatting"""
        if not csv_data:
            return ""

        # Replace escaped newlines with actual newlines
        cleaned = (
            csv_data.replace("\\r\\n", "\n").replace("\\r", "\n").replace("\r", "\n")
        )

        # Remove extra quotes and clean up formatting
        cleaned = re.sub(r'""', '"', cleaned)

        # Ensure proper line endings
        lines = [line.strip() for line in cleaned.split("\n") if line.strip()]

        return "\n".join(lines)

    def _parse_csv_to_structured(self, csv_data: str) -> List[Dict[str, str]]:
        """Parse CSV data into structured format for better AI analysis"""
        if not csv_data:
            return []

        lines = csv_data.strip().split("\n")
        if len(lines) < 2:
            return []

        # Get headers
        headers = [h.strip().strip('"') for h in lines[0].split(",")]

        # Parse data rows
        structured_data = []
        for line in lines[1:]:
            if line.strip():
                values = [v.strip().strip('"') for v in line.split(",")]
                if len(values) == len(headers):
                    row_dict = dict(zip(headers, values))
                    structured_data.append(row_dict)

        return structured_data

    def generate_visual_summary(self, visual_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate AI summary for a single visual

        Args:
            visual_data: Cleaned data for a single visual

        Returns:
            Dictionary containing executive summary and detailed analysis
        """
        title = visual_data.get("title", "Untitled Visual")
        structured_data = visual_data.get("structured_data", [])

        if not structured_data:
            return {
                "executive_summary": f"No data available for analysis in {title}.",
                "detailed_analysis": "Unable to perform analysis due to lack of data.",
            }

        # Create prompt for AI analysis
        prompt = self._create_analysis_prompt(title, structured_data)

        try:
            response = self.model.generate_content(prompt)
            analysis = response.text

            # Parse the response to extract executive summary and detailed analysis
            return self._parse_ai_response(analysis)

        except Exception as e:
            return {
                "executive_summary": f"Error generating summary for {title}: {str(e)}",
                "detailed_analysis": "Unable to complete analysis due to technical error.",
            }

    def _create_analysis_prompt(self, title: str, data: List[Dict[str, str]]) -> str:
        """Create a comprehensive prompt for AI analysis"""

        # Sample a few rows for analysis (limit to avoid token limits)
        sample_data = data[:10] if len(data) > 10 else data

        prompt = f"""
Analyze the following Power BI visual data and provide insights:

Visual Title: {title}

Data Sample:
{json.dumps(sample_data, indent=2)}

Total Records: {len(data)}

Please provide your analysis in the following format:

EXECUTIVE SUMMARY:
[Provide a concise 2-3 sentence executive summary highlighting the key findings and business insights]

DETAILED ANALYSIS:
[Provide a comprehensive analysis including:
- Key trends and patterns identified
- Notable data points or outliers
- Business implications and recommendations
- Statistical insights where relevant
- Actionable insights for decision-making]

Focus on business value and actionable insights rather than just describing the data.
"""
        return prompt

    def _parse_ai_response(self, response: str) -> Dict[str, str]:
        """Parse AI response to extract executive summary and detailed analysis"""

        # Split response by sections
        sections = response.split("DETAILED ANALYSIS:")

        if len(sections) >= 2:
            executive_part = sections[0].replace("EXECUTIVE SUMMARY:", "").strip()
            detailed_part = sections[1].strip()
        else:
            # Fallback if format is not as expected
            # lines = response.split('\n')
            executive_part = response[:200] + "..." if len(response) > 200 else response
            detailed_part = response

        return {"executive_summary": executive_part, "detailed_analysis": detailed_part}

    def generate_comprehensive_summary(
        self, all_visuals_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate comprehensive summary for all visuals

        Args:
            all_visuals_data: All cleaned visual data

        Returns:
            Dictionary containing summaries for each visual and overall insights
        """
        summaries = {}

        for visual_key, visual_data in all_visuals_data.items():
            summaries[visual_key] = self.generate_visual_summary(visual_data)

        # Generate overall insights
        overall_insights = self._generate_overall_insights(summaries)

        return {
            "visual_summaries": summaries,
            "overall_insights": overall_insights,
            "metadata": {
                "total_visuals_analyzed": len(summaries),
                "analysis_timestamp": (
                    visual_data.get("exportedAt", "") if all_visuals_data else ""
                ),
            },
        }

    def _generate_overall_insights(self, summaries: Dict[str, Dict[str, str]]) -> str:
        """Generate overall insights from all visual summaries"""

        if not summaries:
            return "No data available for overall analysis."

        # Combine all executive summaries for overall insight
        all_summaries = []
        for visual_key, summary in summaries.items():
            all_summaries.append(
                f"{visual_key}: {summary.get('executive_summary', '')}"
            )

        combined_text = "\n".join(all_summaries)

        prompt = f"""
Based on the following individual visual summaries from a Power BI report, provide overall strategic insights:

{combined_text}

Please provide:
1. Key themes and patterns across all visuals
2. Strategic recommendations for the business
3. Areas that require immediate attention
4. Overall performance assessment

Keep the response concise but comprehensive.
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Unable to generate overall insights: {str(e)}"
