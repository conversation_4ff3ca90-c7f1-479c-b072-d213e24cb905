// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

$(function () {
    var reportContainer = $("#report-container").get(0);
    powerbi.bootstrap(reportContainer, { type: "report" });

    var models = window["powerbi-client"].models;
    var reportLoadConfig = {
        type: "report",
        tokenType: models.TokenType.Embed
    };

    let report;

    $.ajax({
        type: "GET",
        url: "/getembedinfo",
        dataType: "json",
        success: function (data) {
            embedData = $.parseJSON(JSON.stringify(data));
            reportLoadConfig.accessToken = embedData.accessToken;
            reportLoadConfig.embedUrl = embedData.reportConfig[0].embedUrl;
            tokenExpiry = embedData.tokenExpiry;

            report = powerbi.embed(reportContainer, reportLoadConfig);

            report.on("loaded", function () {
                console.log("Report load successful");
            });

            report.on("rendered", function () {
                console.log("Report render successful");
            });

            report.off("error");
            report.on("error", function (event) {
                var errorMsg = event.detail;
                console.error(errorMsg);
                return;
            });

            report.on("dataSelected", function (event) {
                var selectionData = event.detail;
                console.log("Raw selection data:", selectionData);

                if (selectionData && selectionData.dataPoints) {
                    const selected = selectionData.dataPoints.map(dp => {
                        return {
                            visualTitle: selectionData.visualTitle,
                            category: dp.identityFields?.[0]?.displayName || 'Unknown',
                            value: dp.values?.[0] || 'N/A'
                        };
                    });

                    console.log("Parsed selection:", selected);
                    // Optional: sendToFlow(selected);
                }
            });
        },
        error: function (err) {
            var errorContainer = $(".error-container");
            $(".embed-container").hide();
            errorContainer.show();

            var errMessageHtml = "<strong> Error Details: </strong> <br/>" + $.parseJSON(err.responseText)["errorMsg"];
            errMessageHtml = errMessageHtml.split("\n").join("<br/>");
            errorContainer.html(errMessageHtml);
        }
    });

    // ✅ Print as PDF
    $("#print-report").on("click", function () {
        if (report) {
            report.print()
                .then(() => console.log("Print dialog opened"))
                .catch(err => console.error("Print failed:", err));
        } else {
            console.error("Report is not loaded yet.");
        }
    });

    // ✅ Export data from ALL visuals and send to Power Automate
    $("#export-data").on("click", async function () {
        if (!report) return console.error("Report not available yet.");

        try {
            const pages = await report.getPages();
            const activePage = pages.find(p => p.isActive);
            const visuals = await activePage.getVisuals();

            console.log(`Found ${visuals.length} visuals on the page`);

            // Export data from all visuals
            const allVisualsData = {};
            let exportedCount = 0;

            for (let i = 0; i < visuals.length; i++) {
                const visual = visuals[i];
                try {
                    console.log(`Exporting data from visual ${i + 1}: ${visual.type} (${visual.title || 'No title'})`);

                    const exportResult = await visual.exportData(models.ExportDataType.Summarized, 1000);

                    // Create a unique key for each visual
                    const visualKey = visual.title || `Visual_${i + 1}_${visual.type}`;

                    allVisualsData[visualKey] = {
                        title: visual.title || `Visual ${i + 1}`,
                        data: exportResult.data,
                        exportedAt: new Date().toISOString()
                    };

                    exportedCount++;
                    console.log(`Successfully exported data from: ${visualKey}`);

                } catch (visualError) {
                    console.warn(`Failed to export data from visual ${i + 1} (${visual.type}):`, visualError);
                    // Continue with other visuals even if one fails
                }
            }

            if (exportedCount === 0) {
                return alert("No visual data could be exported.");
            }

            console.log(`Successfully exported data from ${exportedCount} visuals:`, allVisualsData);

            // Send all visuals data to backend endpoint
            const response = await fetch("/sendtoflow", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({
                    data: allVisualsData,
                    metadata: {
                        totalVisuals: visuals.length,
                        exportedVisuals: exportedCount,
                        pageName: activePage.displayName || activePage.name,
                        exportTimestamp: new Date().toISOString()
                    }
                })
            });

            const result = await response.json();

            if (response.ok) {
                alert(`${result.message || "Data sent to Power Automate successfully!"}`);
            } else {
                console.error("Power Automate error:", result.errorMsg);
                alert("Failed to send data to Power Automate: " + (result.errorMsg || "Unknown error"));
            }
        } catch (err) {
            console.error("Error exporting chart data:", err);
            alert("Failed to export chart data: " + err.message);
        }
    });
});
